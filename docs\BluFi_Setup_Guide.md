# 鱼缸传感器 BluFi 配网指南

## 概述

本项目已更新为使用 BluFi (Bluetooth WiFi Provisioning) 进行 WiFi 配网，替代了硬编码的 WiFi 凭据。这使得设备可以在不修改代码的情况下连接到不同的 WiFi 网络。

## 功能特性

### 1. BluFi 配网模式
- **初次上电**：如果没有存储的 WiFi 配置或无法连接 WiFi，自动进入配网模式
- **配网超时**：3分钟内没有收到网络配置，设备永久进入 deep sleep 模式
- **配网成功**：WiFi 凭据自动保存到设备的非易失性存储器(NVS)

### 2. 失败重试机制
- **唤醒连接**：从 deep sleep 唤醒后，使用存储的凭据尝试连接 WiFi
- **失败计数**：连续三次连接网络失败，设备永久进入 deep sleep 模式
- **计数重置**：成功连接后，失败计数自动重置为0

### 3. 永久深度睡眠
- **触发条件**：
  - 初次配网超时（3分钟）
  - 连续3次 WiFi 连接失败
- **睡眠状态**：设备不会自动唤醒，需要手动重置才能重新启动

## 使用方法

### 1. 安装配网应用

在手机上安装 Espressif 官方的 WiFi 配网应用：

- **Android**: [ESP BLE Provisioning](https://play.google.com/store/apps/details?id=com.espressif.provble)
- **iOS**: [ESP BLE Provisioning](https://apps.apple.com/in/app/esp-ble-provisioning/id1473590141)

### 2. 设备配网流程

1. **设备启动**：
   - 首次上电或清除配置后，设备会自动进入配网模式
   - 串口监视器会显示配网信息和二维码

2. **手机连接**：
   - 打开 ESP BLE Provisioning 应用
   - 点击 "Provision Device"
   - 选择 "I don't have a QR code"
   - 在设备列表中找到 "PROV_FishTank"

3. **输入凭据**：
   - 输入验证码：`fishtank123`
   - 选择要连接的 WiFi 网络
   - 输入 WiFi 密码
   - 点击连接

4. **配网完成**：
   - 设备会自动连接到指定的 WiFi 网络
   - 凭据会保存到设备存储器中
   - 后续启动会自动使用保存的凭据

### 3. 串口监视器信息

配网过程中，串口监视器会显示：

```
=== Fish Tank Sensor System ===
Firmware Version: 1.0.0
Device: FishTankSensor

ConnectivityManager initialized for device: fish-tank-xxxxxxxxxxxx
MQTT Server: *************:18084
WiFi failure count: 0

--- WiFi Connection Process ---
No stored WiFi credentials found
Starting BluFi provisioning...
Service name: PROV_FishTank
Proof of possession: fishtank123
Timeout: 180000 ms

BluFi provisioning started
Use the ESP BLE Provisioning app to configure WiFi

[QR Code displayed here]

Received WiFi credentials via BluFi
    SSID: YourWiFiNetwork
    Password: YourPassword
BluFi provisioning successful!
WiFi connected! IP address: *************
```

## 配置参数

### BluFi 设置
- **服务名称**: `PROV_FishTank`
- **验证码**: `fishtank123`
- **配网超时**: 3分钟 (180秒)

### WiFi 连接设置
- **连接超时**: 30秒
- **最大失败次数**: 3次
- **失败后操作**: 永久深度睡眠

## 故障排除

### 1. 配网失败
- **检查手机蓝牙**：确保蓝牙已开启
- **检查应用权限**：确保应用有位置权限
- **重新启动设备**：按下 RST 按钮重新开始配网

### 2. WiFi 连接失败
- **检查网络信号**：确保设备在 WiFi 覆盖范围内
- **检查密码**：确保输入的 WiFi 密码正确
- **检查网络类型**：确保是 2.4GHz 网络（ESP32-C3 不支持 5GHz）

### 3. 清除配置
如需重新配网，可以：
- 在代码中调用 `connectivity.clearStoredCredentials()`
- 或者使用 ESP32 的 NVS 擦除功能

## 技术细节

### 存储机制
- WiFi 凭据存储在 ESP32-C3 的 NVS (Non-Volatile Storage) 中
- 失败计数存储在 Preferences 库中
- 设备重启后配置会自动恢复

### 安全性
- 使用 NETWORK_PROV_SECURITY_1 安全级别
- 需要验证码 (Proof of Possession) 才能连接
- 蓝牙连接有超时保护

### 电源管理
- 配网失败或连接失败后进入永久深度睡眠
- 最大程度降低功耗
- 避免无限重试导致的电池耗尽

## 注意事项

1. **分区方案**：由于 BluFi 功能占用较多空间，建议使用 "Huge APP (3MB No OTA/1MB SPIFFS)" 分区方案

2. **网络兼容性**：ESP32-C3 只支持 2.4GHz WiFi 网络，不支持 5GHz

3. **蓝牙干扰**：配网过程中避免其他蓝牙设备的干扰

4. **电源稳定性**：配网过程中确保设备电源稳定，避免意外断电

## 更新日志

- **v1.0.0**: 实现 BluFi 配网功能
- 移除硬编码 WiFi 凭据
- 添加失败重试机制
- 添加永久深度睡眠保护
