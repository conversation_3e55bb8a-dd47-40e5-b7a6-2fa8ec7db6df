; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-devkitm-1]
platform = espressif32
board = esp32-c3-devkitm-1
framework = arduino
lib_deps =
    knolleary/PubSubClient@^2.8
    bblanchon/<PERSON>rd<PERSON><PERSON><PERSON><PERSON>@^7.0.4
monitor_speed = 115200
board_build.partitions = huge_app.csv
board_build.flash_size = 4MB
build_flags =
    -DCORE_DEBUG_LEVEL=0
    -Os
    -ffunction-sections
    -fdata-sections
    -Wl,--gc-sections
